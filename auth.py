from functools import wraps
from flask import request, jsonify, current_app
import logging

def require_auth(f):
    """
    认证装饰器
    检查HTTP请求头中的Authorization Bearer token
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 获取Authorization头
        auth_header = request.headers.get('Authorization')
        
        if not auth_header:
            current_app.logger.warning("请求缺少Authorization头")
            return jsonify({
                'result': False,
                'message': '缺少认证信息'
            }), 401
        
        # 检查Bearer token格式
        if not auth_header.startswith('Bearer '):
            current_app.logger.warning(f"Authorization头格式错误: {auth_header}")
            return jsonify({
                'result': False,
                'message': '认证格式错误'
            }), 401
        
        # 提取token
        token = auth_header[7:]  # 去掉"Bearer "前缀
        
        # 验证token
        expected_token = current_app.config.get('AUTH_TOKEN', 'SecSales-9XbR2pL7vNqY4wTzKmP1x')
        
        if token != expected_token:
            current_app.logger.warning(f"无效的认证token: {token}")
            return jsonify({
                'result': False,
                'message': '认证失败'
            }), 401
        
        current_app.logger.debug("认证成功")
        return f(*args, **kwargs)
    
    return decorated_function

def get_current_token():
    """
    获取当前请求的token（用于调试或日志记录）
    """
    auth_header = request.headers.get('Authorization', '')
    if auth_header.startswith('Bearer '):
        return auth_header[7:]
    return None
