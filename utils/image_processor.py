import os
import re
from typing import List, Dict, Tuple
from bs4 import BeautifulSoup
from PIL import Image
import logging

from models.qa_models import ProcessedImage

logger = logging.getLogger(__name__)

class ImageProcessor:
    """图片处理工具类"""
    
    def __init__(self, config):
        self.config = config
        self.allowed_extensions = config.get('ALLOWED_EXTENSIONS', {'png', 'jpg', 'jpeg'})
        self.upload_folder = config.get('UPLOAD_FOLDER', 'uploads')
        
    def extract_images_from_html(self, html_content: str) -> List[str]:
        """从HTML内容中提取图片URL"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            img_tags = soup.find_all('img')
            
            image_urls = []
            for img in img_tags:
                src = img.get('src')
                if src:
                    image_urls.append(src)
            
            logger.info(f"从HTML中提取到{len(image_urls)}个图片URL")
            return image_urls
            
        except Exception as e:
            logger.error(f"提取图片URL时发生错误: {e}")
            return []
    
    def validate_uploaded_files(self, files: Dict) -> Tuple[List[str], List[str]]:
        """
        验证上传的文件
        返回: (有效文件列表, 错误信息列表)
        """
        valid_files = []
        errors = []

        for key, file in files.items():
            if file and file.filename:
                # 检查文件扩展名
                if self._allowed_file(file.filename):
                    try:
                        # 尝试打开图片验证格式
                        img = Image.open(file.stream)
                        img.verify()
                        file.stream.seek(0)  # 重置文件指针
                        valid_files.append(key)
                        logger.debug(f"文件{file.filename}验证通过")
                    except Exception as e:
                        errors.append(f"文件{file.filename}不是有效的图片格式: {e}")
                        logger.warning(f"文件{file.filename}验证失败: {e}")
                else:
                    errors.append(f"文件{file.filename}格式不支持")
                    logger.warning(f"文件{file.filename}格式不支持")

        return valid_files, errors
    
    def _allowed_file(self, filename: str) -> bool:
        """检查文件扩展名是否允许"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in self.allowed_extensions
    
    def replace_image_urls_in_html(self, html_content: str, img_mapping: Dict[str, str]) -> str:
        """
        替换HTML中的图片URL
        img_mapping: {<img url=XXXXX>: new_text}
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            img_tags = soup.find_all('img')
            for img in img_tags:
                src = img.get('src')
                file_key = re.search(r'/img/([^/]+)\.(?:jpg|jpeg|png)', src).group(1)
                if file_key and file_key in img_mapping:
                    img.replace_with(img_mapping[file_key])
                    logger.debug(f"替换图片URL: {img} -> {img_mapping[file_key]}")
            
            return str(soup)
            
        except Exception as e:
            logger.error(f"替换图片URL时发生错误: {e}")
            return html_content
    
    def add_image_understanding_to_html(self, html_content: str, understanding_mapping: Dict[str, str]) -> str:
        """
        在HTML中添加图片理解文本
        understanding_mapping: {图片URL: 理解文本}
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            img_tags = soup.find_all('img')
            
            for img in img_tags:
                src = img.get('src')
                if src and src in understanding_mapping:
                    understanding_text = understanding_mapping[src]
                    if understanding_text:
                        # 在图片后添加理解文本
                        understanding_p = soup.new_tag('p')
                        understanding_p.string = f"[图片描述: {understanding_text}]"
                        understanding_p['class'] = 'image-understanding'
                        img.insert_after(understanding_p)
                        logger.debug(f"为图片{src}添加理解文本")
            
            return str(soup)
            
        except Exception as e:
            logger.error(f"添加图片理解文本时发生错误: {e}")
            return html_content

    def remove_html_tags(self, html_content: str) -> str:
        """去除HTML标签，保留纯文本"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            return soup.get_text(separator=' ', strip=True)
        except Exception as e:
            logger.error(f"去除HTML标签时发生错误: {e}")
            return html_content
