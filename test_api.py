#!/usr/bin/env python3
"""
API测试脚本
用于测试/kbs/api/add/qa接口
"""

import requests
import json
import os
from io import BytesIO
from PIL import Image

# 配置
BASE_URL = "http://localhost:5000"
AUTH_TOKEN = "SecSales-9XbR2pL7vNqY4wTzKmP1x"

def create_test_image():
    """创建一个测试图片"""
    img = Image.new('RGB', (100, 100), color='red')
    img_bytes = BytesIO()
    img.save(img_bytes, format='PNG')
    img_bytes.seek(0)
    return img_bytes

def test_health_check():
    """测试健康检查接口"""
    print("测试健康检查接口...")
    
    response = requests.get(f"{BASE_URL}/health")
    
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    print("-" * 50)

def test_add_qa_without_auth():
    """测试无认证的请求"""
    print("测试无认证请求...")
    
    data = {
        "product_name": "测试产品",
        "question": "<p>这是一个测试问题</p>",
        "answer": "<p>这是一个测试答案</p>"
    }
    
    response = requests.post(
        f"{BASE_URL}/kbs/api/add/qa",
        data={"data": json.dumps(data)}
    )
    
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    print("-" * 50)

def test_add_qa_with_auth():
    """测试带认证的请求"""
    print("测试带认证的请求...")
    
    headers = {
        "Authorization": f"Bearer {AUTH_TOKEN}"
    }
    
    data = {
        "product_name": "测试产品",
        "question": "<p>这是一个测试问题，包含图片：<img src='test1.png' /></p>",
        "answer": "<p>这是一个测试答案，包含图片：<img src='test2.png' /></p>"
    }
    
    # 创建测试图片
    test_image1 = create_test_image()
    test_image2 = create_test_image()
    
    files = {
        "file_1": ("test1.png", test_image1, "image/png"),
        "file_2": ("test2.png", test_image2, "image/png")
    }
    
    form_data = {
        "data": json.dumps(data)
    }
    
    response = requests.post(
        f"{BASE_URL}/kbs/api/add/qa",
        headers=headers,
        data=form_data,
        files=files
    )
    
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    print("-" * 50)

def test_add_qa_invalid_data():
    """测试无效数据"""
    print("测试无效数据...")
    
    headers = {
        "Authorization": f"Bearer {AUTH_TOKEN}"
    }
    
    # 缺少必要字段的数据
    data = {
        "product_name": "",  # 空产品名称
        "question": "",      # 空问题
        "answer": ""         # 空答案
    }
    
    response = requests.post(
        f"{BASE_URL}/kbs/api/add/qa",
        headers=headers,
        data={"data": json.dumps(data)}
    )
    
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    print("-" * 50)

def main():
    """主测试函数"""
    print("开始API测试...")
    print("=" * 50)
    
    try:
        # 测试健康检查
        test_health_check()
        
        # 测试无认证请求
        test_add_qa_without_auth()
        
        # 测试带认证的请求
        test_add_qa_with_auth()
        
        # 测试无效数据
        test_add_qa_invalid_data()
        
        print("测试完成!")
        
    except requests.exceptions.ConnectionError:
        print("连接失败！请确保服务器正在运行在 http://localhost:5000")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    main()
