from dataclasses import dataclass
from typing import List, Optional, Dict, Any
import json

@dataclass
class QAData:
    """Q&A数据模型"""
    product_name: str           # 产品名称
    question: str              # 问题描述(Q) - HTML格式
    answer: str                # 解决方案(A) - HTML格式
    
    @classmethod
    def from_json(cls, json_str: str) -> 'QAData':
        """从JSON字符串创建QAData实例"""
        try:
            data = json.loads(json_str)
            return cls(
                product_name=data.get('productName', ''),
                question=data.get('question', ''),
                answer=data.get('answer', '')
            )
        except (json.JSONDecodeError, KeyError) as e:
            raise ValueError(f"无效的JSON数据: {e}")
    
    def validate(self) -> List[str]:
        """验证数据有效性，返回错误信息列表"""
        errors = []
        
        if not self.product_name or not self.product_name.strip():
            errors.append("产品名称不能为空")
        
        if not self.question or not self.question.strip():
            errors.append("问题描述不能为空")
        
        if not self.answer or not self.answer.strip():
            errors.append("解决方案不能为空")
        
        # 字数限制检查（去除HTML标签后的纯文本长度）
        from bs4 import BeautifulSoup
        
        question_text = BeautifulSoup(self.question, 'html.parser').get_text()
        if len(question_text) > 1000:
            errors.append(f"问题描述超过1000字限制，当前{len(question_text)}字")
        
        answer_text = BeautifulSoup(self.answer, 'html.parser').get_text()
        if len(answer_text) > 2000:
            errors.append(f"解决方案超过2000字限制，当前{len(answer_text)}字")
        
        return errors

@dataclass
class ProcessedImage:
    """处理后的图片信息"""
    original_url: str          # 原始图片URL
    dify_file_id: str         # Dify文件ID
    understanding_text: str = ""  # 图片理解文本
    
@dataclass
class DocumentInfo:
    """文档信息"""
    document_id: str
    title: str

@dataclass
class SegmentInfo:
    """分段信息"""
    segment_id: str
    document_id: str
    content: str
    
@dataclass
class QAProcessResult:
    """Q&A处理结果"""
    result: bool               # 是否成功
    message: str              # 结果消息
    document_id: Optional[str] = None    # 文档ID
    segment_id: Optional[str] = None     # 分段ID
    kb_id: Optional[str] = None          # 知识库ID
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        response = {
            'result': self.result,
            'message': self.message
        }
        
        if self.document_id:
            response['documentId'] = self.document_id
        if self.segment_id:
            response['segmentId'] = self.segment_id
        if self.kb_id:
            response['kbId'] = self.kb_id
            
        return response
