#!/usr/bin/env python3
"""
应用启动脚本
"""

import os
import sys
from app import create_app

def main():
    """主函数"""
    # 创建Flask应用
    app = create_app()
    
    # 获取端口号
    port = int(os.environ.get('PORT', 5000))
    host = os.environ.get('HOST', '0.0.0.0')
    
    print(f"启动AIServer...")
    print(f"服务地址: http://{host}:{port}")
    print(f"健康检查: http://{host}:{port}/health")
    print(f"API接口: http://{host}:{port}/kbs/api/add/qa")
    print("-" * 50)
    
    # 启动应用
    try:
        app.run(host=host, port=port, debug=app.config['DEBUG'])
    except KeyboardInterrupt:
        print("\n应用已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
