from flask import Flask, request, jsonify
from flask_cors import CORS
import logging
import os
from datetime import datetime

from config import Config
from auth import require_auth
from services.qa_service import QAService
from utils.logger import setup_logger

def create_app():
    """创建Flask应用"""
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # 初始化配置
    Config.init_app(app)
    
    # 启用CORS
    CORS(app)
    
    # 设置日志
    setup_logger(app)
    
    # 注册路由
    register_routes(app)
    
    # 注册错误处理
    register_error_handlers(app)
    
    return app

def register_routes(app):
    """注册路由"""
    
    @app.route('/health', methods=['GET'])
    def health_check():
        """健康检查接口"""
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'service': 'AIServer'
        })
    
    @app.route('/kbs/api/add/qa', methods=['POST'])
    @require_auth
    def add_qa_to_kb():
        """提交Q&A到FAQ知识库接口"""
        try:
            app.logger.info("收到添加Q&A到知识库的请求")
            
            # 获取请求数据
            data = request.form.get('data')
            files = request.files
            
            if not data:
                return jsonify({
                    'result': False,
                    'message': '缺少data参数'
                }), 400
            
            # 创建QA服务实例
            qa_service = QAService(app.config)
            
            # 处理Q&A添加请求
            result = qa_service.add_qa_to_knowledge_base(data, files)
            
            app.logger.info(f"Q&A添加处理完成: {result}")
            
            return jsonify(result)
            
        except Exception as e:
            app.logger.error(f"处理Q&A添加请求时发生错误: {str(e)}", exc_info=True)
            return jsonify({
                'result': False,
                'message': f'服务器内部错误: {str(e)}'
            }), 500

def register_error_handlers(app):
    """注册错误处理器"""
    
    @app.errorhandler(400)
    def bad_request(error):
        return jsonify({
            'result': False,
            'message': '请求参数错误'
        }), 400
    
    @app.errorhandler(401)
    def unauthorized(error):
        return jsonify({
            'result': False,
            'message': '认证失败'
        }), 401
    
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({
            'result': False,
            'message': '接口不存在'
        }), 404
    
    @app.errorhandler(413)
    def request_entity_too_large(error):
        return jsonify({
            'result': False,
            'message': '上传文件过大'
        }), 413
    
    @app.errorhandler(500)
    def internal_error(error):
        return jsonify({
            'result': False,
            'message': '服务器内部错误'
        }), 500

if __name__ == '__main__':
    app = create_app()
    port = int(os.environ.get('PORT', 5000))
    app.run(host='0.0.0.0', port=port, debug=app.config['DEBUG'])
