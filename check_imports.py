#!/usr/bin/env python3
"""
检查项目导入是否正常
"""

def check_imports():
    """检查所有模块导入"""
    print("检查项目导入...")
    
    try:
        print("✓ 检查config模块...")
        import config
        
        print("✓ 检查auth模块...")
        import auth
        
        print("✓ 检查models模块...")
        from models.qa_models import QAData, QAProcessResult
        
        print("✓ 检查utils模块...")
        from utils.logger import setup_logger
        from utils.image_processor import ImageProcessor
        
        print("✓ 检查clients模块...")
        from clients.dify_client import DifyAPIClient
        
        print("✓ 检查services模块...")
        from services.qa_service import QAService
        from services.image_understanding_service import ImageUnderstandingService
        
        print("✓ 检查app模块...")
        from app import create_app
        
        print("\n所有模块导入成功！")
        return True
        
    except ImportError as e:
        print(f"✗ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"✗ 其他错误: {e}")
        return False

def check_config():
    """检查配置"""
    print("\n检查配置...")
    
    try:
        from config import Config
        
        print(f"✓ SECRET_KEY: {'已设置' if Config.SECRET_KEY else '未设置'}")
        print(f"✓ AUTH_TOKEN: {Config.AUTH_TOKEN}")
        print(f"✓ DEBUG: {Config.DEBUG}")
        print(f"✓ DIFY_API_BASE_URL: {Config.DIFY_API_BASE_URL}")
        print(f"✓ DIFY_API_KEY: {'已设置' if Config.DIFY_API_KEY else '未设置'}")
        print(f"✓ DIFY_DATASET_ID: {'已设置' if Config.DIFY_DATASET_ID else '未设置'}")
        print(f"✓ UPLOAD_FOLDER: {Config.UPLOAD_FOLDER}")
        print(f"✓ ENABLE_IMAGE_UNDERSTANDING: {Config.ENABLE_IMAGE_UNDERSTANDING}")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置检查错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("SecSales AI Server - 项目检查")
    print("=" * 50)
    
    # 检查导入
    import_ok = check_imports()
    
    # 检查配置
    config_ok = check_config()
    
    print("\n" + "=" * 50)
    if import_ok and config_ok:
        print("✓ 项目检查通过！可以启动服务器。")
        print("\n启动命令:")
        print("  python run.py")
        print("  或")
        print("  python app.py")
    else:
        print("✗ 项目检查失败！请修复上述问题。")
    
    print("=" * 50)

if __name__ == '__main__':
    main()
